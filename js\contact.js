// Contact page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    initContactForm();
    initMapInteraction();
    initFormValidation();
    initAccordion();
});

// Contact form functionality
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleContactFormSubmission(this);
    });
    
    // Real-time validation
    const formInputs = contactForm.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// Handle contact form submission
function handleContactFormSubmission(form) {
    const formData = new FormData(form);
    const loader = window.SweetDelights.showLoading();
    
    // Validate all fields
    if (!validateContactForm(formData)) {
        window.SweetDelights.hideLoading(loader);
        return;
    }
    
    // Simulate form submission
    setTimeout(() => {
        window.SweetDelights.hideLoading(loader);
        
        // Show success message
        window.SweetDelights.showNotification(
            'Thank you for your message! We will get back to you within 24 hours.',
            'success'
        );
        
        // Reset form
        form.reset();
        clearAllErrors();
        
        // Send confirmation
        sendContactConfirmation(formData);
        
    }, 2000);
}

// Validate contact form
function validateContactForm(formData) {
    let isValid = true;
    const errors = [];
    
    // Required fields validation
    const requiredFields = {
        'first_name': 'First name',
        'last_name': 'Last name',
        'email': 'Email address',
        'subject': 'Subject',
        'message': 'Message'
    };
    
    for (const [field, label] of Object.entries(requiredFields)) {
        const value = formData.get(field);
        if (!value || value.trim().length === 0) {
            errors.push(`${label} is required.`);
            highlightFieldError(field);
            isValid = false;
        }
    }
    
    // Email validation
    const email = formData.get('email');
    if (email && !window.SweetDelights.validateEmail(email)) {
        errors.push('Please enter a valid email address.');
        highlightFieldError('email');
        isValid = false;
    }
    
    // Message length validation
    const message = formData.get('message');
    if (message && message.trim().length < 10) {
        errors.push('Message must be at least 10 characters long.');
        highlightFieldError('message');
        isValid = false;
    }
    
    // Privacy policy agreement
    const privacy = formData.get('privacy');
    if (!privacy) {
        errors.push('You must agree to the privacy policy.');
        highlightFieldError('privacy');
        isValid = false;
    }
    
    // Show errors
    if (!isValid) {
        window.SweetDelights.showNotification(
            errors.join(' '),
            'error'
        );
    }
    
    return isValid;
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';
    
    // Required field check
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required.';
    }
    
    // Specific validations
    switch (fieldName) {
        case 'email':
            if (value && !window.SweetDelights.validateEmail(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address.';
            }
            break;
        case 'phone':
            if (value && value.length < 10) {
                isValid = false;
                errorMessage = 'Please enter a valid phone number.';
            }
            break;
        case 'message':
            if (value && value.length < 10) {
                isValid = false;
                errorMessage = 'Message must be at least 10 characters long.';
            }
            break;
        case 'first_name':
        case 'last_name':
            if (value && value.length <1) {
                isValid = false;
                errorMessage = 'Name must be at least 2 characters long.';
            }
            break;
    }
    
    if (isValid) {
        clearFieldError(field);
    } else {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

// Highlight field error
function highlightFieldError(fieldName) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    if (field) {
        field.classList.add('is-invalid');
    }
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // Add error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorMessage = field.parentNode.querySelector('.invalid-feedback');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Clear all errors
function clearAllErrors() {
    const invalidFields = document.querySelectorAll('.is-invalid');
    invalidFields.forEach(field => {
        field.classList.remove('is-invalid');
    });
    
    const errorMessages = document.querySelectorAll('.invalid-feedback');
    errorMessages.forEach(message => {
        message.remove();
    });
}

// Send contact confirmation
function sendContactConfirmation(formData) {
    const name = `${formData.get('first_name')} ${formData.get('last_name')}`;
    const email = formData.get('email');
    const subject = formData.get('subject');
    
    console.log('Contact Form Submission:', {
        name: name,
        email: email,
        subject: subject,
        timestamp: new Date().toISOString()
    });
    
    // Simulate email confirmation
    setTimeout(() => {
        window.SweetDelights.showNotification(
            `Confirmation email sent to ${email}`,
            'info'
        );
    }, 3000);
}

// Map interaction
function initMapInteraction() {
    const mapContainer = document.querySelector('.map-wrapper');
    const mapIframe = mapContainer?.querySelector('iframe');
    
    if (!mapContainer || !mapIframe) return;
    
    // Add click overlay to enable map interaction
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        cursor: pointer;
        z-index: 10;
    `;
    
    mapContainer.style.position = 'relative';
    mapContainer.appendChild(overlay);
    
    // Remove overlay on click to enable map interaction
    overlay.addEventListener('click', function() {
        this.style.display = 'none';
        window.SweetDelights.showNotification('Click and drag to explore the map', 'info');
        
        // Re-enable overlay after 10 seconds
        setTimeout(() => {
            this.style.display = 'block';
        }, 10000);
    });
    
    // Add map loading animation
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'map-loading';
    loadingDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading map...</span>
            </div>
            <p class="mt-2">Loading map...</p>
        </div>
    `;
    loadingDiv.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 245, 225, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 5;
    `;
    
    mapContainer.appendChild(loadingDiv);
    
    // Hide loading when iframe loads
    mapIframe.addEventListener('load', function() {
        setTimeout(() => {
            loadingDiv.style.opacity = '0';
            setTimeout(() => loadingDiv.remove(), 300);
        }, 1000);
    });
}

// Form validation styling
function initFormValidation() {
    // Add custom validation styles
    const style = document.createElement('style');
    style.textContent = `
        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
        }
        
        .form-control:valid,
        .form-select:valid {
            border-color: #28a745;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
        }
    `;
    document.head.appendChild(style);
}

// Accordion functionality enhancement
function initAccordion() {
    const accordionButtons = document.querySelectorAll('.accordion-button');
    
    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add smooth scroll to accordion item
            setTimeout(() => {
                const accordionItem = this.closest('.accordion-item');
                const offset = accordionItem.offsetTop - 100;
                
                window.scrollTo({
                    top: offset,
                    behavior: 'smooth'
                });
            }, 300);
        });
    });
}

// Auto-save form data
function initAutoSave() {
    const form = document.getElementById('contactForm');
    if (!form) return;
    
    const formInputs = form.querySelectorAll('input, select, textarea');
    
    // Load saved data
    formInputs.forEach(input => {
        const savedValue = localStorage.getItem(`contact_${input.name}`);
        if (savedValue && input.type !== 'checkbox') {
            input.value = savedValue;
        } else if (savedValue && input.type === 'checkbox') {
            input.checked = savedValue === 'true';
        }
    });
    
    // Save data on input
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.type === 'checkbox') {
                localStorage.setItem(`contact_${this.name}`, this.checked);
            } else {
                localStorage.setItem(`contact_${this.name}`, this.value);
            }
        });
    });
    
    // Clear saved data on successful submission
    form.addEventListener('submit', function() {
        formInputs.forEach(input => {
            localStorage.removeItem(`contact_${input.name}`);
        });
    });
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initAutoSave();
    
    // Add contact card animations
    const contactCards = document.querySelectorAll('.contact-card');
    contactCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.animation = 'fadeInUp 0.6s ease forwards';
    });
    
    // Add FAQ search functionality
    initFAQSearch();
});

// FAQ search functionality
function initFAQSearch() {
    const faqSection = document.querySelector('.faq-section');
    if (!faqSection) return;
    
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search FAQs...';
    searchInput.className = 'form-control mb-4';
    searchInput.style.maxWidth = '400px';
    searchInput.style.margin = '0 auto 2rem';
    
    const faqTitle = faqSection.querySelector('.section-title');
    faqTitle.parentNode.insertBefore(searchInput, faqTitle.nextSibling);
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const accordionItems = document.querySelectorAll('.accordion-item');
        
        accordionItems.forEach(item => {
            const question = item.querySelector('.accordion-button').textContent.toLowerCase();
            const answer = item.querySelector('.accordion-body').textContent.toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
}
