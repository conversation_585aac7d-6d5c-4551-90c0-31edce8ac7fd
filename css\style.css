/* Beautiful Baking Website Design - <PERSON> Sai Baking Classes */

/* Modern Baking Color Palette */
:root {
    /* Primary Colors */
    --primary-brown: #8B4513;
    --primary-cream: #FFF8DC;
    --primary-white: #FFFFFF;
    /* --primary-gold: #DAA520; */

    /* Secondary Colors */
    /* --secondary-pink: #FFB6C1; */
    --secondary-peach: #FFDAB9;
    --secondary-lavender: #E6E6FA;
    --secondary-mint: #F0FFF0;

    /* Accent Colors */
    --accent-chocolate: #D2691E;
    --accent-caramel: #DEB887;
    /* --accent-strawberry: #FF69B4; */
    --accent-vanilla: #F5DEB3;

    /* Gradient Colors */
    /* --gradient-warm: linear-gradient(135deg, #FFB6C1 0%, #FFDAB9 50%, #F5DEB3 100%); */
    --gradient-chocolate: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    /* --gradient-sweet: linear-gradient(135deg, #FFB6C1 0%, #E6E6FA 100%); */
    --gradient-cream: linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 100%);

    /* Shadows */
    --shadow-soft: 0 5px 15px rgba(139, 69, 19, 0.1);
    --shadow-medium: 0 10px 25px rgba(139, 69, 19, 0.15);
    --shadow-strong: 0 15px 35px rgba(139, 69, 19, 0.2);
    --shadow-cake: 0 8px 32px rgba(255, 182, 193, 0.3);

    /* Border Radius */
    --radius-small: 10px;
    --radius-medium: 20px;
    --radius-large: 30px;
    --radius-xl: 40px;

    /* Transitions */
    --transition-fast: 0.3s ease-in-out;
    --transition-medium: 0.5s ease-in-out;
    --transition-slow: 0.8s ease-in-out;
}

/* Global Styles - Beautiful Baking Design */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
    color: var(--primary-brown);
    background: var(--primary-cream);
    overflow-x: hidden;
    position: relative;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Beautiful Local Baking Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        url('../images/background/background1.jpg') center/cover,
        var(--gradient-cream);
    background-blend-mode: overlay;
    opacity: 0.15;
    z-index: -2;
    pointer-events: none;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(255, 182, 193, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 218, 185, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(230, 230, 250, 0.05) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

/* Beautiful Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: 600;
    color: var(--primary-brown);
    line-height: 1.3;
    margin: 0 0 1.5rem 0;
    text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

h1 {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 700;
    line-height: 1.2;
    background: var(--gradient-chocolate);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
h2 {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 600;
    color: var(--accent-chocolate);
}
h3 {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 600;
    color: var(--primary-brown);
}
h4 {
    font-size: clamp(1.25rem, 2.5vw, 2rem);
    font-weight: 500;
    color: var(--accent-chocolate);
}
h5 {
    font-size: clamp(1.125rem, 2vw, 1.75rem);
    font-weight: 500;
}
h6 {
    font-size: clamp(1rem, 1.5vw, 1.5rem);
    font-weight: 500;
}

p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--primary-brown);
    margin: 0 0 1.5rem 0;
    font-weight: 400;
    opacity: 0.9;
}

.lead {
    font-size: 1.3rem;
    font-weight: 500;
    line-height: 1.6;
    color: var(--accent-chocolate);
    font-style: italic;
}

/* Beautiful Section Styling */
.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 2rem;
    color: var(--primary-brown);
    text-align: center;
    font-weight: 700;
    position: relative;
    font-family: 'Playfair Display', Georgia, serif;
}

.section-title::before {
    content: '🧁';
    position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
    opacity: 0.7;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-warm);
    border-radius: 10px;
    box-shadow: var(--shadow-cake);
}

.section-subtitle {
    font-size: 1.3rem;
    color: var(--accent-chocolate);
    margin-bottom: 3rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    line-height: 1.7;
    font-style: italic;
}

/* Beautiful Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
}

/* Remove excessive spacing - Compact Design */
.py-5 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
}

.py-4 {
    padding-top: 30px !important;
    padding-bottom: 30px !important;
}

.mb-4 {
    margin-bottom: 15px !important;
}

.mb-5 {
    margin-bottom: 20px !important;
}

.mb-3 {
    margin-bottom: 15px !important;
}

.mt-4 {
    margin-top: 20px !important;
}

.mt-5 {
    margin-top: 25px !important;
}

/* Compact card spacing */
.card {
    margin-bottom: 15px;
}

/* Reduce row gaps */
.row {
    margin-bottom: 0;
}

.row > [class*="col-"] {
    margin-bottom: 15px;
}

/* Beautiful Sweet Navigation */
.navbar {
    background: rgba(255, 248, 220, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 2px solid var(--secondary-pink);
    transition: all var(--transition-medium);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-cake);
}

.navbar.scrolled {
    background: rgba(255, 248, 220, 0.98);
    padding: 0.75rem 0;
    box-shadow: var(--shadow-medium);
    border-bottom: 3px solid var(--accent-strawberry);
}

.navbar-brand {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--primary-brown) !important;
    display: flex;
    align-items: center;
    transition: all var(--transition-medium);
    text-decoration: none;
    padding: 0.5rem 0;
}

.navbar-brand:hover {
    color: var(--accent-strawberry) !important;
    text-decoration: none;
    transform: translateY(-2px) scale(1.02);
}

.navbar-brand .logo-img {
    height: 120px;
    width: auto;
    margin-right: 1rem;
    border-radius: var(--radius-large);
    /* background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.95),
        rgba(255, 248, 220, 0.95)); */
    padding: 0.75rem;
    /* box-shadow:
        0 8px 25px rgba(255, 182, 193, 0.6),
        0 4px 12px rgba(139, 69, 19, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.8); */
    /* border: 3px solid var(--secondary-pink); */
    filter: brightness(1.15) contrast(1.15) saturate(1.1);
}

.navbar-brand:hover .logo-img {
    /* box-shadow:
        0 12px 35px rgba(255, 182, 193, 0.8),
        0 6px 18px rgba(139, 69, 19, 0.6),
        inset 0 3px 6px rgba(255, 255, 255, 0.9); */
    /* border-color: var(--accent-strawberry); */
    filter: brightness(1.25) contrast(1.25) saturate(1.2);
}

.navbar-brand .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.navbar-brand .brand-text .main-text {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-brown) !important;
    display: block !important;
    visibility: visible !important;
    text-shadow: 2px 2px 4px rgba(255, 182, 193, 0.3);
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -0.01em;
}

.navbar-brand .brand-text .sub-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--accent-chocolate) !important;
    display: block !important;
    visibility: visible !important;
    text-shadow: 1px 1px 2px rgba(255, 182, 193, 0.2);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.02em;
    font-style: italic;
}

.nav-link {
    font-weight: 500;
    color: var(--primary-brown) !important;
    margin: 0 0.5rem;
    padding: 0.75rem 1.5rem !important;
    position: relative;
    transition: all var(--transition-medium);
    font-size: 1.1rem;
    border-radius: var(--radius-large);
    text-decoration: none;
    font-family: 'Inter', sans-serif;
    background: rgba(255, 182, 193, 0.1);
    border: 2px solid transparent;
}

.nav-link::before {
    content: '🍰';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    opacity: 0;
    transition: all var(--transition-medium);
}

.nav-link:hover {
    color: var(--accent-strawberry) !important;
    background: var(--gradient-sweet);
    text-decoration: none;
    transform: translateY(-3px);
    border-color: var(--secondary-pink);
    box-shadow: var(--shadow-cake);
}

.nav-link:hover::before {
    opacity: 1;
    top: -15px;
}

.nav-link.active {
    color: var(--primary-white) !important;
    background: var(--gradient-chocolate);
    font-weight: 600;
    box-shadow: var(--shadow-strong);
    border-color: var(--accent-chocolate);
}

.nav-link.active::before {
    content: '✨';
    opacity: 1;
    top: -15px;
}

/* Luxury Mobile Navigation Toggle */
.navbar-toggler {
    border: 2px solid rgba(212, 175, 55, 0.3);
    padding: 0.5rem;
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    background: rgba(212, 175, 55, 0.1);
}

.navbar-toggler:hover {
    border-color: var(--primary-gold);
    background: rgba(212, 175, 55, 0.2);
    transform: scale(1.05);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
    outline: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28212, 175, 55, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2.5' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5rem;
    height: 1.5rem;
}

/* Luxury Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    margin-top: 0;
}

.carousel-item {
    min-height: 100vh;
    position: relative;
}

.hero-slide {
    min-height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.carousel-fade .carousel-item {
    opacity: 0;
    transition: opacity 1.5s ease-in-out;
}

.carousel-fade .carousel-item.active {
    opacity: 1;
}

/* Beautiful Local Cake Background Images */
.hero-slide-1 {
    background: linear-gradient(135deg, rgba(255, 182, 193, 0.4) 0%, rgba(255, 248, 220, 0.5) 100%),
                url('../images/background/background1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.hero-slide-2 {
    background: linear-gradient(135deg, rgba(255, 218, 185, 0.4) 0%, rgba(230, 230, 250, 0.5) 100%),
                url('../images/background/background1.jpg');
    background-size: cover;
    background-position: center top;
    background-attachment: fixed;
    filter: hue-rotate(30deg) brightness(1.1);
}

.hero-slide-3 {
    background: linear-gradient(135deg, rgba(245, 222, 179, 0.4) 0%, rgba(255, 182, 193, 0.5) 100%),
                url('../images/background/background1.jpg');
    background-size: cover;
    background-position: center bottom;
    background-attachment: fixed;
    filter: hue-rotate(-20deg) brightness(1.05) saturate(1.1);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(26, 26, 46, 0.1);
    backdrop-filter: blur(1px);
}

.hero-title {
    font-size: clamp(3rem, 7vw, 6rem);
    font-weight: 700;
    color: var(--primary-brown);
    margin-bottom: 2rem;
    line-height: 1.1;
    text-align: center;
    font-family: 'Playfair Display', Georgia, serif;
    text-shadow: 3px 3px 6px rgba(255, 255, 255, 0.8);
    letter-spacing: -0.02em;
    background: var(--gradient-chocolate);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(2px 2px 4px rgba(139, 69, 19, 0.3));
}

.hero-subtitle {
    font-size: clamp(1.25rem, 3vw, 1.8rem);
    color: var(--primary-brown);
    margin-bottom: 3rem;
    font-weight: 500;
    max-width: 800px;
    line-height: 1.7;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
    margin-left: auto;
    margin-right: auto;
    background: rgba(255, 248, 220, 0.9);
    padding: 1rem 2rem;
    border-radius: var(--radius-large);
    border: 2px solid var(--secondary-pink);
    box-shadow: var(--shadow-cake);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* Carousel Indicators */
.carousel-indicators {
    bottom: 2rem;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid var(--primary-gold);
    transition: all var(--transition-medium);
}

.carousel-indicators .active {
    background-color: var(--primary-gold);
    transform: scale(1.2);
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: all var(--transition-medium);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-color: rgba(212, 175, 55, 0.8);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.carousel-control-prev-icon:hover,
.carousel-control-next-icon:hover {
    background-color: var(--primary-gold);
    transform: scale(1.1);
}

/* Sweet Beautiful Buttons */
.btn-primary {
    background: var(--gradient-warm);
    border: 3px solid var(--secondary-pink);
    color: var(--primary-brown);
    font-weight: 600;
    padding: 1.2rem 2.5rem;
    border-radius: var(--radius-xl);
    transition: all var(--transition-medium);
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-cake);
    font-family: 'Inter', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.btn-primary::before {
    content: '🍰';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
    opacity: 0;
    transition: all var(--transition-medium);
}

.btn-primary:hover::before {
    opacity: 1;
    top: -5px;
    right: -5px;
    animation: bounce 0.6s ease-in-out;
}

.btn-primary:hover {
    background: var(--gradient-sweet);
    color: var(--accent-chocolate);
    text-decoration: none;
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-strong);
    border-color: var(--accent-strawberry);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.btn-outline-primary {
    border: 2px solid var(--primary-gold);
    color: var(--primary-gold);
    background: transparent;
    font-weight: 600;
    padding: 0.875rem 1.875rem;
    border-radius: var(--radius-large);
    transition: all var(--transition-medium);
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
}

.btn-outline-primary:hover {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
    color: var(--primary-navy);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-gold);
}

.btn-outline-light {
    border: 3px solid var(--primary-white);
    color: var(--primary-brown);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-weight: 600;
    padding: 1.1rem 2.3rem;
    border-radius: var(--radius-xl);
    transition: all var(--transition-medium);
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    font-family: 'Inter', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    overflow: hidden;
}

.btn-outline-light::before {
    content: '✨';
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 1.2rem;
    opacity: 0;
    transition: all var(--transition-medium);
}

.btn-outline-light:hover::before {
    opacity: 1;
    top: -5px;
    left: -5px;
    animation: sparkle 0.8s ease-in-out;
}

.btn-outline-light:hover {
    background: var(--primary-white);
    border-color: var(--accent-strawberry);
    color: var(--accent-chocolate);
    text-decoration: none;
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-strong);
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Beautiful Welcome Section */
.welcome-section {
    background: var(--gradient-cream);
    padding: 8rem 0;
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('../images/background/background1.jpg') center/cover,
        var(--gradient-sweet);
    background-blend-mode: overlay;
    opacity: 0.12;
    z-index: 0;
    filter: blur(1px) brightness(1.1);
}

.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(255, 182, 193, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 218, 185, 0.2) 0%, transparent 40%);
    z-index: 1;
}

.welcome-section .container {
    position: relative;
    z-index: 2;
}

.welcome-content h2 {
    color: var(--primary-navy);
    margin-bottom: 2rem;
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 600;
    font-family: 'Playfair Display', Georgia, serif;
}

.welcome-content .lead {
    color: var(--primary-gold);
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 2rem;
    font-style: italic;
}

.welcome-content p {
    color: var(--secondary-dark);
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.welcome-stats {
    margin-top: 3rem;
    padding: 2rem 0;
}

.stat-number {
    font-size: clamp(2.5rem, 4vw, 4rem);
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    font-family: 'Playfair Display', Georgia, serif;
    text-shadow: 2px 2px 4px rgba(212, 175, 55, 0.2);
}

.stat-label {
    font-size: 1rem;
    color: var(--secondary-dark);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.welcome-image {
    position: relative;
}

.welcome-image img,
.welcome-image > div {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-strong);
    transition: all var(--transition-medium);
    overflow: hidden;
}

.welcome-image:hover img,
.welcome-image:hover > div {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 60px rgba(26, 26, 46, 0.2);
}

/* Beautiful Services Preview */
.services-preview {
    background: var(--secondary-mint);
    padding: 8rem 0;
    position: relative;
    overflow: hidden;
}

.services-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('../images/background/background1.jpg') center/cover,
        var(--gradient-sweet);
    background-blend-mode: overlay;
    opacity: 0.1;
    z-index: 0;
    filter: blur(2px) brightness(1.2) hue-rotate(15deg);
}

.services-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 105, 180, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 218, 185, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.services-preview .container {
    position: relative;
    z-index: 2;
}

.services-preview .section-title {
    color: var(--primary-navy);
}

.services-preview .section-subtitle {
    color: var(--secondary-dark);
    opacity: 0.8;
}

.service-card {
    background: var(--primary-white);
    padding: 3rem 2rem;
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-cake);
    transition: all var(--transition-medium);
    height: 100%;
    border: 3px solid var(--secondary-pink);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-warm);
    transform: scaleX(0);
    transition: transform var(--transition-medium);
}

.service-card::after {
    content: '🧁';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    opacity: 0.3;
    transition: all var(--transition-medium);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover::after {
    opacity: 1;
    transform: scale(1.2) rotate(15deg);
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--shadow-strong);
    background: var(--gradient-cream);
    border-color: var(--accent-strawberry);
}

.service-icon {
    width: 90px;
    height: 90px;
    background: var(--gradient-warm);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    border: 4px solid var(--secondary-pink);
    box-shadow: var(--shadow-cake);
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    transition: left 0.8s ease;
}

.service-card:hover .service-icon::before {
    left: 100%;
}

.service-card:hover .service-icon {
    transform: scale(1.2) rotate(360deg);
    box-shadow: var(--shadow-strong);
    border-color: var(--accent-strawberry);
    background: var(--gradient-sweet);
}

.service-icon i {
    font-size: 2.5rem;
    color: var(--primary-brown);
    z-index: 1;
    position: relative;
    transition: all var(--transition-medium);
}

.service-card:hover .service-icon i {
    color: var(--accent-chocolate);
    transform: scale(1.1);
}

.service-card h4 {
    color: var(--primary-navy);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    font-family: 'Playfair Display', Georgia, serif;
}

.service-card p {
    color: var(--secondary-dark);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1rem;
    opacity: 0.9;
}

/* Luxury Workshop Section */
.workshop-section {
    background: var(--gradient-cream);
    padding: 8rem 0;
    position: relative;
    overflow: hidden;
}

.workshop-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(255, 107, 107, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(212, 175, 55, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.workshop-section .container {
    position: relative;
    z-index: 1;
}

.workshop-section .section-title {
    color: var(--primary-navy);
    margin-bottom: 3rem;
    position: relative;
}

.workshop-section .section-title::after {
    background: linear-gradient(135deg, var(--accent-coral) 0%, var(--primary-gold) 100%);
}

.workshop-section .card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    overflow: hidden;
    margin-bottom: 2rem;
    background: var(--primary-white);
    position: relative;
}

.workshop-section .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--accent-coral) 0%, var(--primary-gold) 100%);
}

.workshop-section .card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
}

.workshop-section .card-header {
    background: linear-gradient(135deg, var(--accent-coral) 0%, var(--primary-gold) 100%);
    border: none;
    padding: 1.5rem 2rem;
    color: var(--primary-white);
}

.workshop-section .card-header h5 {
    color: var(--primary-white);
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.workshop-section .card-body {
    padding: 2rem;
    background: var(--primary-white);
}

.workshop-section .alert {
    border: none;
    border-radius: var(--radius-large);
    padding: 2rem;
    margin: 2rem 0;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(212, 175, 55, 0.1) 100%);
    border-left: 4px solid var(--accent-coral);
}

.workshop-section .alert h5 {
    color: var(--primary-navy);
    font-weight: 600;
    margin-bottom: 1rem;
}

.workshop-section .alert p {
    color: var(--secondary-dark);
    margin: 0;
    font-weight: 500;
}

/* Luxury Gallery Preview */
.gallery-preview {
    padding: 8rem 0;
    background: var(--primary-white);
    position: relative;
    overflow: hidden;
}

.gallery-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(80, 200, 120, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.gallery-preview .container {
    position: relative;
    z-index: 1;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-soft);
    transition: all var(--transition-medium);
    background: var(--primary-white);
    margin-bottom: 2rem;
    group: gallery;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-strong);
}

.gallery-item img,
.gallery-item > div {
    width: 100%;
    height: 320px;
    object-fit: cover;
    transition: all var(--transition-medium);
    border-radius: var(--radius-xl);
}

.gallery-item:hover img,
.gallery-item:hover > div {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(212, 175, 55, 0.6) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-medium);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(5px);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay .btn {
    background: var(--primary-white);
    border: 2px solid var(--primary-white);
    color: var(--primary-navy);
    font-weight: 600;
    border-radius: var(--radius-large);
    padding: 1rem 2rem;
    font-size: 1rem;
    transition: all var(--transition-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.gallery-overlay .btn:hover {
    background: transparent;
    color: var(--primary-white);
    transform: scale(1.05);
}

/* Beautiful Sweet Footer */
.footer {
    background: var(--gradient-chocolate);
    color: var(--primary-white);
    padding: 5rem 0 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 -10px 30px rgba(139, 69, 19, 0.3);
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('../images/background/background1.jpg') center/cover,
        var(--gradient-chocolate);
    background-blend-mode: overlay;
    opacity: 0.15;
    z-index: 0;
    filter: brightness(0.8) contrast(1.2);
}

.footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-warm);
    z-index: 1;
}

.footer > .container {
    position: relative;
    z-index: 2;
}

.footer p {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    line-height: 1.7;
    font-size: 1rem;
}

.footer h5,
.footer h6 {
    color: var(--primary-white);
    margin-bottom: 2rem;
    font-weight: 600;
    font-size: 1.25rem;
    font-family: 'Playfair Display', Georgia, serif;
    position: relative;
}

.footer h5::after,
.footer h6::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--primary-gold);
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo .logo-img {
    height: 90px;
    width: auto;
    margin-right: 1rem;
    filter: brightness(1.3) contrast(1.2) saturate(1.1);
    border-radius: var(--radius-medium);
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.9),
        rgba(255, 248, 220, 0.9));
    padding: 0.5rem;
    border: 2px solid var(--secondary-pink);
    box-shadow:
        0 6px 20px rgba(255, 182, 193, 0.5),
        0 3px 10px rgba(139, 69, 19, 0.3);
    transition: all var(--transition-medium);
}

.footer-logo:hover .logo-img {
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.4) contrast(1.3) saturate(1.2);
    box-shadow:
        0 8px 25px rgba(255, 182, 193, 0.7),
        0 4px 12px rgba(139, 69, 19, 0.4);
}

.footer-logo .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.footer-logo .brand-text .main-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-white);
    font-family: 'Playfair Display', Georgia, serif;
}

.footer-logo .brand-text .sub-text {
    font-size: 1rem;
    font-weight: 400;
    color: var(--primary-gold);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.02em;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 1rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all var(--transition-medium);
    font-size: 1rem;
    font-weight: 400;
    position: relative;
    display: inline-block;
}

.footer-links a::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--primary-gold);
    transition: width var(--transition-medium);
}

.footer-links a:hover::before {
    width: 100%;
}

.footer-links a:hover {
    color: var(--primary-gold);
    text-decoration: none;
    transform: translateX(8px);
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    color: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
}

.contact-info i {
    color: var(--primary-gold);
    margin-right: 1rem;
    width: 20px;
    margin-top: 2px;
    flex-shrink: 0;
    font-size: 1.1rem;
}

.social-links {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(212, 175, 55, 0.1);
    color: var(--primary-white);
    border-radius: var(--radius-large);
    transition: all var(--transition-medium);
    text-decoration: none;
    border: 2px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.social-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-gold);
    transition: left var(--transition-medium);
    z-index: 0;
}

.social-links a:hover::before {
    left: 0;
}

.social-links a i {
    position: relative;
    z-index: 1;
    font-size: 1.25rem;
    transition: all var(--transition-medium);
}

.social-links a:hover {
    transform: translateY(-5px) scale(1.1);
    color: var(--primary-navy);
    text-decoration: none;
    border-color: var(--primary-gold);
    box-shadow: var(--shadow-gold);
}

.newsletter-form .form-control {
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--radius-large) 0 0 var(--radius-large);
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--primary-white);
    font-size: 1rem;
    font-weight: 400;
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

.newsletter-form .form-control:focus {
    border-color: var(--primary-gold);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
    outline: none;
}

.newsletter-form .btn {
    border-radius: 0 var(--radius-large) var(--radius-large) 0;
    padding: 1rem 1.5rem;
    border: 2px solid var(--primary-gold);
    background: var(--gradient-gold);
    color: var(--primary-navy);
    font-weight: 600;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.newsletter-form .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.newsletter-form .btn:hover::before {
    left: 100%;
}

.newsletter-form .btn:hover {
    background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-gold);
}

/* Beautiful Page Header */
.page-header {
    background: var(--gradient-sweet);
    padding: 120px 0 60px;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('../images/background/background1.jpg') center/cover,
        var(--gradient-warm);
    background-blend-mode: overlay;
    opacity: 0.18;
    z-index: 0;
    filter: brightness(1.1) saturate(1.1);
}

.page-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 70% 60%, rgba(255, 218, 185, 0.3) 0%, transparent 50%);
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--primary-brown);
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-align: center;
    font-family: 'Playfair Display', Georgia, serif;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
    background: var(--gradient-chocolate);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.4rem;
    color: var(--accent-chocolate);
    margin-bottom: 0;
    font-weight: 500;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Apple-Style About Page */
.our-story {
    background: var(--pure-white);
    padding: 100px 0;
}

.story-content .lead {
    color: var(--accent-orange);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.highlight-item {
    text-align: center;
    padding: 2rem 1rem;
    transition: all var(--transition-medium);
}

.highlight-item:hover {
    transform: translateY(-4px);
}

.highlight-item i {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
}

.highlight-item h5 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.highlight-item p {
    font-size: 1rem;
    color: var(--primary-gray);
    line-height: 1.6;
}

.mission-card,
.vision-card {
    background: var(--pure-white);
    padding: 3rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    height: 100%;
    text-align: center;
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.mission-card:hover,
.vision-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.mission-card:hover .card-icon,
.vision-card:hover .card-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.card-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.value-card {
    padding: 2rem 1rem;
    transition: all var(--transition-medium);
    text-align: center;
}

.value-card:hover {
    transform: translateY(-4px);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: var(--accent-green);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-medium);
}

.value-card:hover .value-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.value-icon i {
    font-size: 1.5rem;
    color: var(--pure-white);
}

.team-card {
    background: var(--pure-white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.team-image img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all var(--transition-medium);
}

.team-card:hover .team-image img {
    transform: scale(1.05);
}

.team-info {
    padding: 2rem 1.5rem;
    text-align: center;
}

.team-role {
    color: var(--accent-blue);
    font-weight: 500;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.team-social {
    margin-top: 1.5rem;
}

.team-social a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--secondary-gray);
    color: var(--primary-black);
    text-align: center;
    line-height: 40px;
    border-radius: var(--radius-medium);
    margin: 0 6px;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.team-social a:hover {
    background-color: var(--accent-blue);
    color: var(--pure-white);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Apple-Style Services Page */
.course-categories {
    background: var(--pure-white);
    padding: 100px 0;
}

.category-card {
    background: var(--pure-white);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    text-align: center;
    height: 100%;
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.category-card:hover .category-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.category-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.category-features {
    list-style: none;
    padding: 0;
    margin-top: 2rem;
    text-align: left;
}

.category-features li {
    padding: 0.8rem 0;
    color: var(--primary-gray);
    position: relative;
    padding-left: 2rem;
    font-size: 1rem;
    line-height: 1.5;
}

.category-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-green);
    font-weight: 600;
    font-size: 1.1rem;
}

.course-card {
    background: var(--pure-white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.course-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.course-card.featured {
    border: 2px solid var(--accent-blue);
}

.course-image {
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: all var(--transition-medium);
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-level {
    position: absolute;
    top: 16px;
    left: 16px;
    background: var(--primary-black);
    color: var(--pure-white);
    padding: 6px 16px;
    border-radius: var(--radius-large);
    font-size: 0.85rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.featured-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--accent-orange);
    color: var(--pure-white);
    padding: 6px 16px;
    border-radius: var(--radius-large);
    font-size: 0.85rem;
    font-weight: 600;
}

.course-content {
    padding: 2rem 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.course-content h4 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.course-content p {
    color: var(--primary-gray);
    margin-bottom: 1.5rem;
    flex-grow: 1;
    line-height: 1.6;
}

.course-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.detail-item i {
    color: var(--gold);
    margin-right: 10px;
    width: 16px;
}

.course-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--chocolate-brown);
}

.price-note {
    font-size: 0.9rem;
    color: var(--dark-brown);
    display: block;
}

.feature-item {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--chocolate-brown), var(--gold));
    color: var(--white);
    border-radius: 15px 15px 0 0;
}

.modal-title {
    color: var(--white);
}

.btn-close {
    filter: invert(1);
}

.form-label {
    color: var(--chocolate-brown);
    font-weight: 500;
}

.form-control,
.form-select {
    border: 2px solid var(--cream-white);
    border-radius: 10px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Gallery Page Styles */
.gallery-filter {
    background-color: var(--light-gray);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-buttons .btn {
    margin: 5px;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-buttons .btn.active {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
}

.gallery-grid {
    background-color: var(--white);
}

.gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.gallery-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-card:hover img {
    transform: scale(1.1);
}

/* Portrait Gallery Card Styles - Fixed Consistent Sizing */
.portrait-card {
    aspect-ratio: 3/4;
    height: 400px;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    background: var(--pure-white);
}

.portrait-card img,
.portrait-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
    transition: all 0.3s ease;
    display: block;
}

.portrait-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.portrait-card:hover img {
    transform: scale(1.05);
}

/* Responsive portrait adjustments - Consistent Heights */
@media (max-width: 768px) {
    .portrait-card {
        height: 320px;
        aspect-ratio: 3/4;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .portrait-card {
        height: 380px;
    }
}

@media (min-width: 1200px) {
    .portrait-card {
        height: 420px;
    }
}

/* Legacy Landscape Gallery Card Styles (for backward compatibility) */
.landscape-card {
    aspect-ratio: 16/9;
    min-height: 320px;
}

.landscape-card img,
.landscape-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
}

.landscape-card .text-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive landscape adjustments */
@media (max-width: 768px) {
    .landscape-card {
        min-height: 250px;
        aspect-ratio: 4/3;
    }
}

@media (min-width: 1200px) {
    .landscape-card {
        min-height: 350px;
    }
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(255, 215, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 15px;
}

.portrait-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: var(--pure-white);
    padding: 1rem;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.portrait-card:hover .gallery-content {
    transform: translateY(0);
}

.gallery-content h5 {
    color: var(--pure-white);
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.gallery-content p {
    font-size: 0.9rem;
    margin-bottom: 0;
    opacity: 0.95;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Apple-Style Contact Page */
.contact-info-section {
    background: var(--pure-white);
    padding: 100px 0;
}

.contact-card {
    background: var(--pure-white);
    padding: 2.5rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    height: 100%;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-blue);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-medium);
}

.contact-card:hover .contact-icon {
    background: var(--accent-orange);
    transform: scale(1.1);
}

.contact-icon i {
    font-size: 2rem;
    color: var(--pure-white);
}

.contact-card h4 {
    color: var(--primary-black);
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-card p {
    color: var(--primary-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-form-container {
    background: var(--pure-white);
    padding: 3rem 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.map-container {
    background: var(--pure-white);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.map-wrapper {
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-small);
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.info-item i {
    margin-right: 15px;
    margin-top: 5px;
    font-size: 1.2rem;
}

.info-content h6 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.info-content p {
    color: var(--dark-brown);
    font-size: 0.9rem;
    margin: 0;
}

.faq-section {
    background-color: var(--white);
}

.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.1);
}

.accordion-button {
    background-color: var(--cream-white);
    color: var(--chocolate-brown);
    font-weight: 500;
    border: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--gold);
    color: var(--chocolate-brown);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    background-color: var(--white);
    color: var(--dark-brown);
    padding: 1.5rem;
}

/* Apple-Style Forms */
.form-control,
.form-select {
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-medium);
    padding: 12px 16px;
    transition: all var(--transition-fast);
    font-size: 1rem;
    background: var(--pure-white);
    color: var(--primary-black);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    outline: none;
}

.form-label {
    color: var(--primary-black);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

/* Apple-Style Modal */
.modal-content {
    border-radius: var(--radius-xl);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--pure-white);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: 24px;
}

.modal-title {
    color: var(--primary-black);
    font-weight: 600;
    font-size: 1.5rem;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    opacity: 0.6;
}

.btn-close:hover {
    opacity: 1;
}

/* Luxury Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-section .carousel-item {
        min-height: 80vh;
    }

    .hero-slide-1,
    .hero-slide-2,
    .hero-slide-3 {
        background-attachment: scroll;
    }

    .navbar-brand .logo-img {
        height: 90px;
    }

    .navbar-brand .brand-text .main-text {
        font-size: 1.3rem;
    }

    .navbar-brand .brand-text .sub-text {
        font-size: 0.85rem;
    }

    .welcome-section,
    .services-preview,
    .gallery-preview,
    .workshop-section {
        padding: 6rem 0;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .hero-section .carousel-item {
        min-height: 70vh;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 1.5rem;
    }

    .hero-subtitle {
        font-size: clamp(1rem, 3vw, 1.25rem);
        margin-bottom: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .navbar {
        padding: 0.75rem 0;
    }

    .navbar-brand .logo-img {
        height: 75px;
        margin-right: 0.75rem;
    }

    .navbar-brand .brand-text .main-text {
        font-size: 1.25rem;
    }

    .navbar-brand .brand-text .sub-text {
        font-size: 0.8rem;
    }

    .nav-link {
        padding: 0.75rem 1rem !important;
        margin: 0.25rem 0;
        font-size: 1rem;
        color: var(--primary-white) !important;
        background: rgba(212, 175, 55, 0.1);
        border-radius: var(--radius-medium);
        border: 1px solid rgba(212, 175, 55, 0.2);
    }

    .welcome-section,
    .services-preview,
    .gallery-preview,
    .workshop-section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: clamp(2rem, 6vw, 2.5rem);
        margin-bottom: 1.5rem;
    }

    .section-subtitle {
        font-size: 1.125rem;
        margin-bottom: 2rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .footer {
        padding: 4rem 0 2rem;
    }

    .footer-logo .logo-img {
        height: 65px;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .hero-section .carousel-item {
        min-height: 60vh;
    }

    .hero-title {
        font-size: clamp(1.75rem, 7vw, 2.5rem);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: clamp(0.95rem, 3vw, 1.125rem);
        padding: 0 1rem;
    }

    .btn-lg {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .service-card {
        padding: 2rem 1rem;
    }

    .service-icon {
        width: 70px;
        height: 70px;
    }

    .service-icon i {
        font-size: 1.75rem;
    }

    .gallery-item img,
    .gallery-item > div {
        height: 280px;
    }

    .welcome-stats .col-4 {
        margin-bottom: 2rem;
    }

    .stat-number {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .navbar-brand .brand-text .main-text {
        font-size: 1.125rem;
    }

    .navbar-brand .brand-text .sub-text {
        font-size: 0.75rem;
    }

    .footer h5,
    .footer h6 {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .footer-links,
    .contact-info {
        text-align: center;
    }

    .newsletter-form .input-group {
        flex-direction: column;
    }

    .newsletter-form .form-control {
        border-radius: var(--radius-large);
        margin-bottom: 1rem;
    }

    .newsletter-form .btn {
        border-radius: var(--radius-large);
        width: 100%;
    }
}

/* Luxury Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Luxury Scroll Behavior */
html {
    scroll-behavior: smooth;
}

/* Luxury Focus States */
*:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
    outline-style: solid;
}

button:focus,
.btn:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.2);
}

/* Luxury Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    transition: opacity var(--transition-medium);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-gold);
    color: var(--primary-navy);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
    box-shadow: var(--shadow-gold);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
}
